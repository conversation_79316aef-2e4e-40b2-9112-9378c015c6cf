from quant.tdx_data import get_data

# 正确的数据，用于测试，不要修改
test_samples = [
    {'code': 'sh688583', 'begin': 20250618, 'end': 20250620, 'nfq': [110.1, 81.98, 76.1], 'qfq': [84.269, 81.98, 76.1], 'hfq': [110.1, 107.109, 99.427]},
    {'code': 'sh603072', 'begin': 20250310, 'end': 20250312, 'nfq': [38.62, 38.88, 38.3], 'qfq': [38.47, 38.88, 38.3], 'hfq': [38.62, 39.032, 38.449]},
    {'code': 'sh601377', 'begin': 20220816, 'end': 20220826, },
]


if __name__ == '__main__':
    for sample_dict in test_samples:
        code = sample_dict['code']
        begin = sample_dict['begin']
        end = sample_dict['end']

        # 不复权
        df_no_fq = get_data(code, fq=0, begin=begin, end=end)
        no_fq = [round(i, 3) for i in df_no_fq["close"].to_list()]
        if 'nfq' in sample_dict:
            assert no_fq == sample_dict['nfq'], f"不复权数据不正确: {no_fq} != {sample_dict['nfq']}"
        else:
            print(f"'nfq': {no_fq}")

        # 前复权
        df_qfq = get_data(code, fq=1, begin=begin, end=end)
        qfq = [round(i, 3) for i in df_qfq["close"].to_list()]
        if 'qfq' in sample_dict:
            assert qfq == sample_dict['qfq'], f"前复权数据不正确: {qfq} != {sample_dict['qfq']}"
        else:
            print(f"'qfq': {qfq}")

        # 后复权
        df_hfq = get_data(code, fq=2, begin=begin, end=end)
        hfq = [round(i, 3) for i in df_hfq["close"].to_list()]
        if 'hfq' in sample_dict:
            assert hfq == sample_dict['hfq'], f"后复权数据不正确: {hfq} != {sample_dict['hfq']}"
        else:
            print(f"'hfq': {hfq}")

        print(f"测试数据 {code} 通过")